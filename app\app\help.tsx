import React, { useState } from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Modal,
  TextInput,
  Alert,
  SafeAreaView,
} from "react-native";

const Help = () => {
  const [expandedFaq, setExpandedFaq] = useState<number | null>(null);
  const [isTicketsSectionOpen, setIsTicketsSectionOpen] = useState(false);
  const [isSupportModalOpen, setIsSupportModalOpen] = useState(false);
  const [supportTickets, setSupportTickets] = useState([
    {
      id: 1,
      subject: "Payment not processed",
      status: "open",
      date: "2024-01-15",
      body: "I made a payment but it hasn't been processed yet. Can you help?",
      response: null,
    },
    {
      id: 2,
      subject: "Account verification issue",
      status: "resolved",
      date: "2024-01-10",
      body: "Having trouble with account verification process.",
      response: "Your account has been successfully verified. Thank you for your patience.",
    },
  ]);
  const [expandedTicket, setExpandedTicket] = useState<number | null>(null);
  const [formData, setFormData] = useState({
    subject: "",
    body: "",
  });

  const faqData = [
    {
      question: "How do I create a trade request?",
      answer: "To create a trade request, navigate to the Search page, find a suitable listing, enter your desired amount, and click 'Send Trade Request'. You'll need to have a recipient account set up before initiating the trade."
    },
    {
      question: "What payment methods are supported?",
      answer: "Remflow supports various payment methods including bank transfers, digital wallets, and cryptocurrency. You can add and manage your payment methods in the 'Saved Payment Methods' section under Accounts."
    },
    {
      question: "How long does it take to process a trade?",
      answer: "Trade processing times vary depending on the payment method and verification requirements. Bank transfers typically take 1-3 business days, while digital wallet transfers are usually processed within minutes."
    },
    {
      question: "What are the fees for using Remflow?",
      answer: "Remflow charges a small service fee for each completed trade. The exact fee depends on the trade amount and payment method used. You can view the fee structure in your account settings."
    },
    {
      question: "How do I verify my account?",
      answer: "Account verification requires uploading a government-issued ID and proof of address. Navigate to Account Settings > Verification and follow the step-by-step process. Verification typically takes 24-48 hours."
    },
    {
      question: "Is my personal information secure?",
      answer: "Yes, Remflow uses bank-level encryption and security measures to protect your personal and financial information. We comply with international data protection standards and never share your information with third parties."
    },
    {
      question: "How do I contact customer support?",
      answer: "You can contact our support team through the Help Desk section. Create a support ticket with your query, attach any relevant evidence, and our team will respond within 24 hours. For urgent issues, include 'URGENT' in your ticket title."
    },
    {
      question: "Can I cancel a trade request?",
      answer: "Yes, you can cancel a trade request before it's accepted by the other party. Once a trade is accepted and in progress, cancellation requires mutual agreement or dispute resolution through our support team."
    }
  ];

  const toggleFaq = (index: number) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  const toggleTicketsSection = () => {
    setIsTicketsSectionOpen(!isTicketsSectionOpen);
  };

  const toggleTicket = (ticketId: number) => {
    setExpandedTicket(expandedTicket === ticketId ? null : ticketId);
  };

  const handleSubmitTicket = () => {
    if (!formData.subject.trim() || !formData.body.trim()) {
      Alert.alert("Error", "Please fill in both subject and description");
      return;
    }

    const newTicket = {
      id: supportTickets.length + 1,
      subject: formData.subject.trim(),
      status: "open",
      date: new Date().toISOString().split('T')[0],
      body: formData.body.trim(),
      response: null,
    };

    setSupportTickets([...supportTickets, newTicket]);
    setFormData({ subject: "", body: "" });
    setIsSupportModalOpen(false);
    Alert.alert("Success", "Support ticket created successfully!");
  };

  const SupportTicketModal = () => (
    <Modal
      visible={isSupportModalOpen}
      animationType="slide"
      transparent={true}
      onRequestClose={() => setIsSupportModalOpen(false)}
    >
      <View className="flex-1 justify-center items-center bg-black/50 px-4">
        <View className="bg-white rounded-2xl w-full max-w-md p-6">
          <View className="flex-row justify-between items-center mb-6">
            <Text className="text-xl font-bold text-gray-800">Create Support Ticket</Text>
            <TouchableOpacity
              onPress={() => setIsSupportModalOpen(false)}
              className="p-2"
            >
              <Text className="text-gray-500 text-lg">✕</Text>
            </TouchableOpacity>
          </View>

          <View className="mb-4">
            <Text className="text-sm font-semibold text-gray-700 mb-2">
              Subject <Text className="text-red-500">*</Text>
            </Text>
            <TextInput
              value={formData.subject}
              onChangeText={(text) => setFormData({ ...formData, subject: text })}
              placeholder="Brief description of your issue"
              className="border border-gray-300 rounded-lg px-3 py-3 text-base"
              maxLength={200}
            />
          </View>

          <View className="mb-6">
            <Text className="text-sm font-semibold text-gray-700 mb-2">
              Description <Text className="text-red-500">*</Text>
            </Text>
            <TextInput
              value={formData.body}
              onChangeText={(text) => setFormData({ ...formData, body: text })}
              placeholder="Please provide detailed information about your issue"
              multiline
              numberOfLines={4}
              className="border border-gray-300 rounded-lg px-3 py-3 text-base h-24"
              textAlignVertical="top"
            />
          </View>

          <View className="flex-row gap-3">
            <TouchableOpacity
              onPress={() => setIsSupportModalOpen(false)}
              className="flex-1 bg-gray-200 rounded-lg py-3"
            >
              <Text className="text-center text-gray-700 font-semibold">Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleSubmitTicket}
              className="flex-1 bg-green-500 rounded-lg py-3"
              disabled={!formData.subject.trim() || !formData.body.trim()}
            >
              <Text className="text-center text-white font-semibold">🎫 Create Ticket</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView className="flex-1">
        {/* Header Section */}
        <View className="px-6 py-8 bg-gradient-to-br from-slate-50 to-slate-200">
          <Text className="text-3xl font-bold text-gray-800 text-center mb-2">
            Help Center
          </Text>
          <Text className="text-sm text-gray-600 text-center">
            Find answers to commonly asked questions
          </Text>
        </View>

        <View className="px-6 py-6">
          {/* Welcome Section */}
          <View className="bg-white rounded-2xl p-8 mb-6 shadow-sm border border-gray-200">
            <View className="items-center">
              <Text className="text-6xl mb-4 opacity-80">❓</Text>
              <Text className="text-2xl font-bold text-gray-800 mb-3 text-center">
                How can we help you?
              </Text>
              <Text className="text-base text-gray-600 text-center leading-6">
                Browse through our frequently asked questions to find quick answers to common queries about Remflow's services.
              </Text>
            </View>
          </View>

          {/* FAQ Section */}
          <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-200">
            <View className="flex-row items-center mb-6">
              <Text className="text-2xl mr-3">📋</Text>
              <Text className="text-xl font-bold text-gray-800">
                Frequently Asked Questions
              </Text>
            </View>

            <View className="space-y-3">
              {faqData.map((faq, index) => (
                <View key={index} className="border border-gray-200 rounded-2xl overflow-hidden">
                  <TouchableOpacity
                    onPress={() => toggleFaq(index)}
                    className={`p-5 flex-row justify-between items-center ${
                      expandedFaq === index ? 'bg-blue-500' : 'bg-white'
                    }`}
                  >
                    <Text className={`flex-1 text-base font-semibold mr-3 ${
                      expandedFaq === index ? 'text-white' : 'text-gray-800'
                    }`}>
                      {faq.question}
                    </Text>
                    <Text className={`text-sm ${
                      expandedFaq === index ? 'text-white rotate-180' : 'text-gray-500'
                    }`}>
                      ▼
                    </Text>
                  </TouchableOpacity>

                  {expandedFaq === index && (
                    <View className="bg-gray-50 border-t border-gray-200 p-5">
                      <Text className="text-sm text-gray-700 leading-6">
                        {faq.answer}
                      </Text>
                    </View>
                  )}
                </View>
              ))}
            </View>
          </View>

          {/* Support Tickets Section */}
          <View className="bg-white rounded-2xl p-6 mb-6 shadow-sm border border-gray-200">
            <TouchableOpacity
              onPress={toggleTicketsSection}
              className="flex-row justify-between items-center mb-4"
            >
              <View className="flex-row items-center">
                <Text className="text-2xl mr-3">🎫</Text>
                <Text className="text-xl font-bold text-gray-800">
                  My Support Tickets
                </Text>
                {supportTickets.length > 0 && (
                  <View className="bg-blue-500 rounded-full px-3 py-1 ml-2">
                    <Text className="text-white text-xs font-semibold">
                      {supportTickets.length}
                    </Text>
                  </View>
                )}
              </View>
              <Text className={`text-lg text-gray-500 ${
                isTicketsSectionOpen ? 'rotate-180' : ''
              }`}>
                ▼
              </Text>
            </TouchableOpacity>

            {isTicketsSectionOpen && (
              <View className="space-y-3">
                {supportTickets.length === 0 ? (
                  <View className="text-center py-8">
                    <Text className="text-4xl mb-4 opacity-60">📝</Text>
                    <Text className="text-lg font-semibold text-gray-700 mb-2">
                      No support tickets yet
                    </Text>
                    <Text className="text-sm text-gray-500">
                      Create your first support ticket to get help from our team.
                    </Text>
                  </View>
                ) : (
                  supportTickets.map((ticket) => (
                    <View key={ticket.id} className="border border-gray-200 rounded-xl overflow-hidden">
                      <TouchableOpacity
                        onPress={() => toggleTicket(ticket.id)}
                        className={`p-4 ${
                          expandedTicket === ticket.id ? 'bg-gray-50 border-b border-gray-200' : 'bg-white'
                        }`}
                      >
                        <View className="flex-row justify-between items-start">
                          <View className="flex-1">
                            <Text className="text-base font-semibold text-gray-800 mb-2">
                              {ticket.subject}
                            </Text>
                            <View className="flex-row items-center space-x-3">
                              <View className={`px-2 py-1 rounded ${
                                ticket.status === 'open'
                                  ? 'bg-yellow-100'
                                  : 'bg-green-100'
                              }`}>
                                <Text className={`text-xs font-semibold uppercase ${
                                  ticket.status === 'open'
                                    ? 'text-yellow-800'
                                    : 'text-green-800'
                                }`}>
                                  {ticket.status}
                                </Text>
                              </View>
                              <Text className="text-xs text-gray-500 font-medium">
                                {ticket.date}
                              </Text>
                            </View>
                          </View>
                          <Text className={`text-base text-gray-500 ml-3 ${
                            expandedTicket === ticket.id ? 'rotate-180' : ''
                          }`}>
                            ▼
                          </Text>
                        </View>
                      </TouchableOpacity>

                      {expandedTicket === ticket.id && (
                        <View className="bg-gray-50 p-4">
                          <View className="bg-white rounded-lg p-4">
                            <View className="mb-4">
                              <Text className="text-sm font-semibold text-gray-700 mb-2">
                                Your Message:
                              </Text>
                              <Text className="text-sm text-gray-600 leading-5">
                                {ticket.body}
                              </Text>
                            </View>

                            {ticket.response && (
                              <View className="bg-green-50 border-l-4 border-green-400 p-4 rounded">
                                <Text className="text-sm font-semibold text-gray-700 mb-2">
                                  Support Response:
                                </Text>
                                <Text className="text-sm text-gray-600 leading-5">
                                  {ticket.response}
                                </Text>
                              </View>
                            )}
                          </View>
                        </View>
                      )}
                    </View>
                  ))
                )}
              </View>
            )}
          </View>

          {/* Contact Section */}
          <View className="items-center">
            <View className="bg-white rounded-2xl p-8 shadow-sm border border-gray-200 max-w-md w-full">
              <View className="items-center">
                <Text className="text-5xl mb-4 opacity-80">💬</Text>
                <Text className="text-xl font-bold text-gray-800 mb-3 text-center">
                  Still need help?
                </Text>
                <Text className="text-base text-gray-600 text-center mb-6 leading-6">
                  Can't find what you're looking for? Our support team is here to help.
                </Text>
                <TouchableOpacity
                  onPress={() => setIsSupportModalOpen(true)}
                  className="bg-green-500 rounded-xl px-6 py-4 shadow-lg"
                >
                  <Text className="text-white font-semibold text-base text-center">
                    🎫 Create Support Ticket
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>

      <SupportTicketModal />
    </SafeAreaView>
  );
};

export default Help;