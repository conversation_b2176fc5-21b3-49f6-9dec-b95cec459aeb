/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/2fa`; params?: Router.UnknownInputParams; } | { pathname: `/accounts`; params?: Router.UnknownInputParams; } | { pathname: `/addlisting`; params?: Router.UnknownInputParams; } | { pathname: `/app.d`; params?: Router.UnknownInputParams; } | { pathname: `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/disputes`; params?: Router.UnknownInputParams; } | { pathname: `/forgetpass`; params?: Router.UnknownInputParams; } | { pathname: `/history`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/mylisting`; params?: Router.UnknownInputParams; } | { pathname: `/personal`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/remflowfunds`; params?: Router.UnknownInputParams; } | { pathname: `/searchads`; params?: Router.UnknownInputParams; } | { pathname: `/status`; params?: Router.UnknownInputParams; } | { pathname: `/survey`; params?: Router.UnknownInputParams; } | { pathname: `/help`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/api/onboarding/loginApi`; params?: Router.UnknownInputParams; } | { pathname: `/api/onboarding/refreshTokenEndpoint`; params?: Router.UnknownInputParams; } | { pathname: `/api/onboarding/registerApi`; params?: Router.UnknownInputParams; } | { pathname: `/context/AuthContext`; params?: Router.UnknownInputParams; } | { pathname: `/context/oldSSE`; params?: Router.UnknownInputParams; } | { pathname: `/context/prevAuth`; params?: Router.UnknownInputParams; } | { pathname: `/context/SSEContext`; params?: Router.UnknownInputParams; } | { pathname: `/utils/axiosInterceptor`; params?: Router.UnknownInputParams; } | { pathname: `/utils/constants`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/editlisting/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/trade/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/2fa`; params?: Router.UnknownOutputParams; } | { pathname: `/accounts`; params?: Router.UnknownOutputParams; } | { pathname: `/addlisting`; params?: Router.UnknownOutputParams; } | { pathname: `/app.d`; params?: Router.UnknownOutputParams; } | { pathname: `/dashboard`; params?: Router.UnknownOutputParams; } | { pathname: `/disputes`; params?: Router.UnknownOutputParams; } | { pathname: `/forgetpass`; params?: Router.UnknownOutputParams; } | { pathname: `/history`; params?: Router.UnknownOutputParams; } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/mylisting`; params?: Router.UnknownOutputParams; } | { pathname: `/personal`; params?: Router.UnknownOutputParams; } | { pathname: `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `/register`; params?: Router.UnknownOutputParams; } | { pathname: `/remflowfunds`; params?: Router.UnknownOutputParams; } | { pathname: `/searchads`; params?: Router.UnknownOutputParams; } | { pathname: `/status`; params?: Router.UnknownOutputParams; } | { pathname: `/survey`; params?: Router.UnknownOutputParams; } | { pathname: `/help`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `/api/onboarding/loginApi`; params?: Router.UnknownOutputParams; } | { pathname: `/api/onboarding/refreshTokenEndpoint`; params?: Router.UnknownOutputParams; } | { pathname: `/api/onboarding/registerApi`; params?: Router.UnknownOutputParams; } | { pathname: `/context/AuthContext`; params?: Router.UnknownOutputParams; } | { pathname: `/context/oldSSE`; params?: Router.UnknownOutputParams; } | { pathname: `/context/prevAuth`; params?: Router.UnknownOutputParams; } | { pathname: `/context/SSEContext`; params?: Router.UnknownOutputParams; } | { pathname: `/utils/axiosInterceptor`; params?: Router.UnknownOutputParams; } | { pathname: `/utils/constants`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/editlisting/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/trade/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/2fa${`?${string}` | `#${string}` | ''}` | `/accounts${`?${string}` | `#${string}` | ''}` | `/addlisting${`?${string}` | `#${string}` | ''}` | `/app.d${`?${string}` | `#${string}` | ''}` | `/dashboard${`?${string}` | `#${string}` | ''}` | `/disputes${`?${string}` | `#${string}` | ''}` | `/forgetpass${`?${string}` | `#${string}` | ''}` | `/history${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `/mylisting${`?${string}` | `#${string}` | ''}` | `/personal${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `/register${`?${string}` | `#${string}` | ''}` | `/remflowfunds${`?${string}` | `#${string}` | ''}` | `/searchads${`?${string}` | `#${string}` | ''}` | `/status${`?${string}` | `#${string}` | ''}` | `/survey${`?${string}` | `#${string}` | ''}` | `/help${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `/api/onboarding/loginApi${`?${string}` | `#${string}` | ''}` | `/api/onboarding/refreshTokenEndpoint${`?${string}` | `#${string}` | ''}` | `/api/onboarding/registerApi${`?${string}` | `#${string}` | ''}` | `/context/AuthContext${`?${string}` | `#${string}` | ''}` | `/context/oldSSE${`?${string}` | `#${string}` | ''}` | `/context/prevAuth${`?${string}` | `#${string}` | ''}` | `/context/SSEContext${`?${string}` | `#${string}` | ''}` | `/utils/axiosInterceptor${`?${string}` | `#${string}` | ''}` | `/utils/constants${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/2fa`; params?: Router.UnknownInputParams; } | { pathname: `/accounts`; params?: Router.UnknownInputParams; } | { pathname: `/addlisting`; params?: Router.UnknownInputParams; } | { pathname: `/app.d`; params?: Router.UnknownInputParams; } | { pathname: `/dashboard`; params?: Router.UnknownInputParams; } | { pathname: `/disputes`; params?: Router.UnknownInputParams; } | { pathname: `/forgetpass`; params?: Router.UnknownInputParams; } | { pathname: `/history`; params?: Router.UnknownInputParams; } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/mylisting`; params?: Router.UnknownInputParams; } | { pathname: `/personal`; params?: Router.UnknownInputParams; } | { pathname: `/profile`; params?: Router.UnknownInputParams; } | { pathname: `/register`; params?: Router.UnknownInputParams; } | { pathname: `/remflowfunds`; params?: Router.UnknownInputParams; } | { pathname: `/searchads`; params?: Router.UnknownInputParams; } | { pathname: `/status`; params?: Router.UnknownInputParams; } | { pathname: `/survey`; params?: Router.UnknownInputParams; } | { pathname: `/help`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `/api/onboarding/loginApi`; params?: Router.UnknownInputParams; } | { pathname: `/api/onboarding/refreshTokenEndpoint`; params?: Router.UnknownInputParams; } | { pathname: `/api/onboarding/registerApi`; params?: Router.UnknownInputParams; } | { pathname: `/context/AuthContext`; params?: Router.UnknownInputParams; } | { pathname: `/context/oldSSE`; params?: Router.UnknownInputParams; } | { pathname: `/context/prevAuth`; params?: Router.UnknownInputParams; } | { pathname: `/context/SSEContext`; params?: Router.UnknownInputParams; } | { pathname: `/utils/axiosInterceptor`; params?: Router.UnknownInputParams; } | { pathname: `/utils/constants`; params?: Router.UnknownInputParams; } | `/+not-found` | `/editlisting/${Router.SingleRoutePart<T>}` | `/trade/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/editlisting/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/trade/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
